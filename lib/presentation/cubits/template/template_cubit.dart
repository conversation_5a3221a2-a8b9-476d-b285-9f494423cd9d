import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../data/models/resume_template_model.dart';


class TemplateState extends Equatable {
  final List<ResumeTemplateModel> templates;
  final List<ResumeTemplateModel> filteredTemplates;
  final List<TemplateCategory> categories;
  final ResumeTemplateModel? selectedTemplate;
  final ResumeTemplateModel? previewTemplate;
  final TemplateCategory? selectedCategory;
  final bool showOnlyFreeTemplates;
  final String searchQuery;
  final bool isLoading;
  final String? errorMessage;

  const TemplateState({
    this.templates = const [],
    this.filteredTemplates = const [],
    this.categories = const [],
    this.selectedTemplate,
    this.previewTemplate,
    this.selectedCategory,
    this.showOnlyFreeTemplates = false,
    this.searchQuery = '',
    this.isLoading = false,
    this.errorMessage,
  });

  TemplateState copyWith({
    List<ResumeTemplateModel>? templates,
    List<ResumeTemplateModel>? filteredTemplates,
    List<TemplateCategory>? categories,
    ResumeTemplateModel? selectedTemplate,
    ResumeTemplateModel? previewTemplate,
    TemplateCategory? selectedCategory,
    bool? showOnlyFreeTemplates,
    String? searchQuery,
    bool? isLoading,
    String? errorMessage,
  }) {
    return TemplateState(
      templates: templates ?? this.templates,
      filteredTemplates: filteredTemplates ?? this.filteredTemplates,
      categories: categories ?? this.categories,
      selectedTemplate: selectedTemplate ?? this.selectedTemplate,
      previewTemplate: previewTemplate,
      selectedCategory: selectedCategory,
      showOnlyFreeTemplates: showOnlyFreeTemplates ?? this.showOnlyFreeTemplates,
      searchQuery: searchQuery ?? this.searchQuery,
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage,
    );
  }

  @override
  List<Object?> get props => [
        templates,
        filteredTemplates,
        categories,
        selectedTemplate,
        previewTemplate,
        selectedCategory,
        showOnlyFreeTemplates,
        searchQuery,
        isLoading,
        errorMessage,
      ];
}

class TemplateCubit extends Cubit<TemplateState> {
  TemplateCubit() : super(const TemplateState());

  static const String _selectedTemplateKey = 'selected_template_id';

  void loadTemplates() {
    emit(state.copyWith(isLoading: true));
    
    try {
      final templates = TemplateRepository.getAllTemplates();
      final categories = TemplateCategory.values;
      
      emit(state.copyWith(
        templates: templates,
        categories: categories,
        isLoading: false,
      ));
      
      // Load saved template selection
      _loadSelectedTemplate();
    } catch (e) {
      emit(state.copyWith(
        isLoading: false,
        errorMessage: 'Failed to load templates: $e',
      ));
    }
  }

  void selectTemplate(ResumeTemplateModel template) async {
    emit(state.copyWith(selectedTemplate: template));
    
    // Save selection to preferences
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_selectedTemplateKey, template.id);
    } catch (e) {
      // Silently fail - not critical
      print('Failed to save template selection: $e');
    }
  }

  void filterByCategory(TemplateCategory? category) {
    if (category == null) {
      emit(state.copyWith(
        filteredTemplates: state.templates,
        selectedCategory: null,
      ));
    } else {
      final filtered = state.templates
          .where((template) => template.category == category)
          .toList();
      
      emit(state.copyWith(
        filteredTemplates: filtered,
        selectedCategory: category,
      ));
    }
  }

  void togglePremiumFilter() {
    final showOnlyFree = !state.showOnlyFreeTemplates;
    
    List<ResumeTemplateModel> filtered;
    if (showOnlyFree) {
      filtered = state.templates.where((template) => !template.isPremium).toList();
    } else {
      filtered = state.selectedCategory != null
          ? state.templates.where((template) => template.category == state.selectedCategory).toList()
          : state.templates;
    }
    
    emit(state.copyWith(
      filteredTemplates: filtered,
      showOnlyFreeTemplates: showOnlyFree,
    ));
  }

  void searchTemplates(String query) {
    if (query.isEmpty) {
      emit(state.copyWith(
        filteredTemplates: state.templates,
        searchQuery: '',
      ));
      return;
    }
    
    final filtered = state.templates.where((template) {
      final nameMatch = template.name.toLowerCase().contains(query.toLowerCase());
      final descriptionMatch = template.description.toLowerCase().contains(query.toLowerCase());
      final categoryMatch = template.category.name.toLowerCase().contains(query.toLowerCase());
      
      return nameMatch || descriptionMatch || categoryMatch;
    }).toList();
    
    emit(state.copyWith(
      filteredTemplates: filtered,
      searchQuery: query,
    ));
  }

  void clearFilters() {
    emit(state.copyWith(
      filteredTemplates: state.templates,
      selectedCategory: null,
      showOnlyFreeTemplates: false,
      searchQuery: '',
    ));
  }

  void _loadSelectedTemplate() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedTemplateId = prefs.getString(_selectedTemplateKey);
      
      if (savedTemplateId != null) {
        final template = TemplateRepository.getTemplateById(savedTemplateId);
        emit(state.copyWith(selectedTemplate: template));
      } else {
        // Use default template
        final defaultTemplate = TemplateRepository.getDefaultTemplate();
        emit(state.copyWith(selectedTemplate: defaultTemplate));
      }
    } catch (e) {
      // Use default template if loading fails
      final defaultTemplate = TemplateRepository.getDefaultTemplate();
      emit(state.copyWith(selectedTemplate: defaultTemplate));
    }
  }

  ResumeTemplateModel getCurrentTemplate() {
    return state.selectedTemplate ?? TemplateRepository.getDefaultTemplate();
  }

  void previewTemplate(ResumeTemplateModel template) {
    emit(state.copyWith(previewTemplate: template));
  }

  void closePreview() {
    emit(state.copyWith(previewTemplate: null));
  }
}
