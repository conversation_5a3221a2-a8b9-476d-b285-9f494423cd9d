import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../cubits/template/template_cubit.dart';
import '../../widgets/common/loading_widget.dart';
import '../../widgets/templates/template_card.dart';
import '../../widgets/templates/template_filter_bar.dart';
import '../../../data/models/resume_template_model.dart';

class TemplatesPage extends StatefulWidget {
  const TemplatesPage({super.key});

  @override
  State<TemplatesPage> createState() => _TemplatesPageState();
}

class _TemplatesPageState extends State<TemplatesPage> {
  late final TemplateCubit _templateCubit;

  @override
  void initState() {
    super.initState();
    _templateCubit = TemplateCubit();
    _templateCubit.loadTemplates();
  }

  @override
  void dispose() {
    _templateCubit.close();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider.value(
      value: _templateCubit,
      child: Scaffold(
        appBar: AppBar(
          title: const Text('Resume Templates'),
          elevation: 0,
          actions: [
            IconButton(
              onPressed: () => _showSearchDialog(context),
              icon: const Icon(Icons.search),
              tooltip: 'Search Templates',
            ),
          ],
        ),
        body: BlocBuilder<TemplateCubit, TemplateState>(
          builder: (context, state) {
            if (state.isLoading) {
              return const LoadingWidget(message: 'Loading templates...');
            }

            if (state.errorMessage != null) {
              return _buildErrorState(context, state.errorMessage!);
            }

            return Column(
              children: [
                TemplateFilterBar(
                  categories: state.categories,
                  selectedCategory: state.selectedCategory,
                  showOnlyFree: state.showOnlyFreeTemplates,
                  onCategorySelected: (category) {
                    _templateCubit.filterByCategory(category);
                  },
                  onTogglePremiumFilter: () {
                    _templateCubit.togglePremiumFilter();
                  },
                  onClearFilters: () {
                    _templateCubit.clearFilters();
                  },
                ),
                Expanded(
                  child: _buildTemplateGrid(context, state),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildErrorState(BuildContext context, String error) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 80,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 24),
            Text(
              'Error Loading Templates',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 16),
            Text(
              error,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            ElevatedButton.icon(
              onPressed: () => _templateCubit.loadTemplates(),
              icon: const Icon(Icons.refresh),
              label: const Text('Try Again'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTemplateGrid(BuildContext context, TemplateState state) {
    final templates = state.filteredTemplates.isNotEmpty 
        ? state.filteredTemplates 
        : state.templates;

    if (templates.isEmpty) {
      return _buildEmptyState(context);
    }

    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.7,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: templates.length,
      itemBuilder: (context, index) {
        final template = templates[index];
        final isSelected = state.selectedTemplate?.id == template.id;
        
        return TemplateCard(
          template: template,
          isSelected: isSelected,
          onTap: () => _selectTemplate(context, template),
          onPreview: () => _previewTemplate(context, template),
        );
      },
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 80,
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(height: 24),
            Text(
              'No Templates Found',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 16),
            Text(
              'Try adjusting your filters or search terms',
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            ElevatedButton.icon(
              onPressed: () => _templateCubit.clearFilters(),
              icon: const Icon(Icons.clear_all),
              label: const Text('Clear Filters'),
            ),
          ],
        ),
      ),
    );
  }

  void _selectTemplate(BuildContext context, ResumeTemplateModel template) {
    _templateCubit.selectTemplate(template);
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Selected "${template.name}" template'),
        action: SnackBarAction(
          label: 'Create Resume',
          onPressed: () => _createResumeWithTemplate(context, template),
        ),
      ),
    );
  }

  void _previewTemplate(BuildContext context, ResumeTemplateModel template) {
    _templateCubit.previewTemplate(template);
    // TODO: Show template preview dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(template.name),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              height: 200,
              width: double.infinity,
              decoration: BoxDecoration(
                color: template.style.primaryColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: template.style.primaryColor),
              ),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.description,
                      size: 48,
                      color: template.style.primaryColor,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Template Preview',
                      style: TextStyle(
                        color: template.style.primaryColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            Text(template.description),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _selectTemplate(context, template);
            },
            child: const Text('Select'),
          ),
        ],
      ),
    );
  }

  void _createResumeWithTemplate(BuildContext context, ResumeTemplateModel template) {
    // TODO: Navigate to resume builder with selected template
    Navigator.of(context).pop(); // Go back to previous page
  }

  void _showSearchDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Search Templates'),
        content: TextField(
          autofocus: true,
          decoration: const InputDecoration(
            hintText: 'Enter template name or category...',
            prefixIcon: Icon(Icons.search),
          ),
          onChanged: (query) => _templateCubit.searchTemplates(query),
        ),
        actions: [
          TextButton(
            onPressed: () {
              _templateCubit.clearFilters();
              Navigator.of(context).pop();
            },
            child: const Text('Clear'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}
