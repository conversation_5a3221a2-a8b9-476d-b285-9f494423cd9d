import 'package:hive/hive.dart';
import '../models/activity_model.dart';

class LocalActivityDataSource {
  static const String _activityBoxName = 'activity';
  static const String _activitiesKey = 'activities';
  static const int _maxActivities = 100; // Limit to last 100 activities

  Future<Box> _getActivityBox() async {
    return await Hive.openBox(_activityBoxName);
  }

  Future<void> saveActivity(ActivityModel activity) async {
    try {
      final box = await _getActivityBox();
      
      // Get existing activities
      final existingActivities = await getAllActivities();
      
      // Add new activity at the beginning (most recent first)
      final updatedActivities = [activity, ...existingActivities];
      
      // Limit to max activities
      final limitedActivities = updatedActivities.take(_maxActivities).toList();
      
      // Save back to storage
      await box.put(_activitiesKey, limitedActivities.map((a) => a.toJson()).toList());
    } catch (e) {
      throw Exception('Failed to save activity locally: $e');
    }
  }

  Future<List<ActivityModel>> getAllActivities() async {
    try {
      final box = await _getActivityBox();
      final activitiesData = box.get(_activitiesKey);

      if (activitiesData != null) {
        final List<dynamic> activitiesList = activitiesData;
        return activitiesList
            .map((data) => ActivityModel.fromJson(Map<String, dynamic>.from(data)))
            .toList();
      }
      return [];
    } catch (e) {
      throw Exception('Failed to get local activities: $e');
    }
  }

  Future<List<ActivityModel>> getRecentActivities({int limit = 20}) async {
    try {
      final allActivities = await getAllActivities();
      return allActivities.take(limit).toList();
    } catch (e) {
      throw Exception('Failed to get recent activities: $e');
    }
  }

  Future<List<ActivityModel>> getActivitiesByType(ActivityType type) async {
    try {
      final allActivities = await getAllActivities();
      return allActivities.where((activity) => activity.type == type).toList();
    } catch (e) {
      throw Exception('Failed to get activities by type: $e');
    }
  }

  Future<List<ActivityModel>> getActivitiesByResumeId(String resumeId) async {
    try {
      final allActivities = await getAllActivities();
      return allActivities
          .where((activity) => activity.resumeId == resumeId)
          .toList();
    } catch (e) {
      throw Exception('Failed to get activities by resume ID: $e');
    }
  }

  Future<List<ActivityModel>> getActivitiesInDateRange({
    required DateTime startDate,
    required DateTime endDate,
  }) async {
    try {
      final allActivities = await getAllActivities();
      return allActivities
          .where((activity) =>
              activity.timestamp.isAfter(startDate) &&
              activity.timestamp.isBefore(endDate))
          .toList();
    } catch (e) {
      throw Exception('Failed to get activities in date range: $e');
    }
  }

  Future<void> deleteActivity(String activityId) async {
    try {
      final allActivities = await getAllActivities();
      final updatedActivities = allActivities
          .where((activity) => activity.id != activityId)
          .toList();
      
      final box = await _getActivityBox();
      await box.put(_activitiesKey, updatedActivities.map((a) => a.toJson()).toList());
    } catch (e) {
      throw Exception('Failed to delete activity: $e');
    }
  }

  Future<void> clearAllActivities() async {
    try {
      final box = await _getActivityBox();
      await box.delete(_activitiesKey);
    } catch (e) {
      throw Exception('Failed to clear all activities: $e');
    }
  }

  Future<void> clearOldActivities({int keepCount = 50}) async {
    try {
      final allActivities = await getAllActivities();
      if (allActivities.length > keepCount) {
        final activitiesToKeep = allActivities.take(keepCount).toList();
        final box = await _getActivityBox();
        await box.put(_activitiesKey, activitiesToKeep.map((a) => a.toJson()).toList());
      }
    } catch (e) {
      throw Exception('Failed to clear old activities: $e');
    }
  }

  Future<int> getActivityCount() async {
    try {
      final activities = await getAllActivities();
      return activities.length;
    } catch (e) {
      throw Exception('Failed to get activity count: $e');
    }
  }

  Future<ActivityModel?> getLastActivity() async {
    try {
      final activities = await getAllActivities();
      return activities.isNotEmpty ? activities.first : null;
    } catch (e) {
      throw Exception('Failed to get last activity: $e');
    }
  }
}
