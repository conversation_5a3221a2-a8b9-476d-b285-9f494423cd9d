import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:uuid/uuid.dart';

import '../models/simple_resume_model.dart';

class FirebaseResumeDataSource {
  final FirebaseFirestore _firestore;
  final FirebaseAuth _firebaseAuth;
  final Uuid _uuid = const Uuid();

  FirebaseResumeDataSource(this._firestore, this._firebaseAuth);

  Future<ResumeModel> getResume(String resumeId) async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      final doc = await _firestore
          .collection('resumes')
          .doc(resumeId)
          .get();

      if (!doc.exists) {
        throw Exception('Resume not found');
      }

      final data = doc.data()!;

      // Verify the resume belongs to the current user
      if (data['userId'] != user.uid) {
        throw Exception('Access denied: Resume does not belong to current user');
      }

      return ResumeModel.fromJson(data);
    } catch (e) {
      throw Exception('Failed to get resume: $e');
    }
  }

  Future<List<ResumeModel>> getUserResumes(String userId) async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      // Ensure the requested userId matches the current user
      if (userId != user.uid) {
        throw Exception('Access denied: Cannot access other user\'s resumes');
      }

      final querySnapshot = await _firestore
          .collection('resumes')
          .where('userId', isEqualTo: userId)
          .orderBy('updatedAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => ResumeModel.fromJson(doc.data()))
          .toList();
    } catch (e) {
      throw Exception('Failed to get user resumes: $e');
    }
  }

  Future<void> saveResume(ResumeModel resume) async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      // Add userId to the resume data and update timestamp
      final resumeData = resume.copyWith(
        updatedAt: DateTime.now(),
      ).toJson();

      // Ensure userId is set
      resumeData['userId'] = user.uid;

      // Save to Firestore
      await _firestore
          .collection('resumes')
          .doc(resume.id)
          .set(resumeData, SetOptions(merge: true));

      // Resume saved successfully
    } catch (e) {
      // Log error for debugging
      throw Exception('Failed to save resume: $e');
    }
  }

  Future<void> deleteResume(String resumeId) async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      // First verify the resume belongs to the current user
      final doc = await _firestore
          .collection('resumes')
          .doc(resumeId)
          .get();

      if (!doc.exists) {
        throw Exception('Resume not found');
      }

      final data = doc.data()!;
      if (data['userId'] != user.uid) {
        throw Exception('Access denied: Resume does not belong to current user');
      }

      // Delete the resume
      await _firestore
          .collection('resumes')
          .doc(resumeId)
          .delete();
    } catch (e) {
      throw Exception('Failed to delete resume: $e');
    }
  }

  Future<ResumeModel> duplicateResume(String resumeId) async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      // Get the original resume
      final originalResume = await getResume(resumeId);
      final newResumeId = _uuid.v4();

      // Create duplicated resume with new ID and timestamps
      final duplicatedResume = originalResume.copyWith(
        id: newResumeId,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Save the duplicated resume to Firestore
      await saveResume(duplicatedResume);

      return duplicatedResume;
    } catch (e) {
      throw Exception('Failed to duplicate resume: $e');
    }
  }

  Future<String> shareResume(String resumeId) async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      // Create a public share link
      final shareData = {
        'resumeId': resumeId,
        'userId': user.uid,
        'createdAt': DateTime.now().toIso8601String(),
        'isPublic': true,
      };

      final shareDoc = await _firestore
          .collection('shared_resumes')
          .add(shareData);

      // Return the share URL
      return 'https://your-app-domain.com/shared/${shareDoc.id}';
    } catch (e) {
      throw Exception('Failed to share resume: $e');
    }
  }


}
