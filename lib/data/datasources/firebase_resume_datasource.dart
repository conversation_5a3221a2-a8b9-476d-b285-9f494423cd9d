import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:uuid/uuid.dart';

import '../models/simple_resume_model.dart';

class FirebaseResumeDataSource {
  final FirebaseFirestore _firestore;
  final FirebaseAuth _firebaseAuth;
  final Uuid _uuid = const Uuid();

  FirebaseResumeDataSource(this._firestore, this._firebaseAuth);

  Future<ResumeModel> getResume(String resumeId) async {
    try {
      final user = _firebaseAuth.currentUser;

      // Get main resume document
      final doc = await _firestore
          .collection('resumes')
          .doc(resumeId)
          .get();

      if (!doc.exists) {
        throw Exception('Resume not found');
      }

      final data = doc.data()!;

      // For testing purposes, allow access to test user data
      const testUserId = 'Bbb0ezu0zeQqCDHghn3LLGXceM93';
      final isTestUserResume = data['userId'] == testUserId;

      if (user == null && !isTestUserResume) {
        throw Exception('User not authenticated');
      }

      // Verify the resume belongs to the current user or is test data
      if (!isTestUserResume && user != null && data['userId'] != user.uid) {
        throw Exception('Access denied: Resume does not belong to current user');
      }

      // Get all subcollections
      final workExperience = await _getSubcollection(resumeId, 'workExperience');
      final education = await _getSubcollection(resumeId, 'education');
      final projects = await _getSubcollection(resumeId, 'projects');
      final skills = await _getSubcollection(resumeId, 'skills');
      final languages = await _getSubcollection(resumeId, 'languages');
      final certifications = await _getSubcollection(resumeId, 'certifications');
      final socialMedia = await _getSubcollection(resumeId, 'socialMedia');

      // Combine main document with subcollections
      final completeResumeData = {
        ...data,
        'workExperience': workExperience,
        'education': education,
        'projects': projects,
        'skills': skills,
        'languages': languages,
        'certifications': certifications,
        'socialMedia': socialMedia,
      };

      return ResumeModel.fromJson(completeResumeData);
    } catch (e) {
      throw Exception('Failed to get resume: $e');
    }
  }

  Future<List<ResumeModel>> getUserResumes(String userId) async {
    try {
      final user = _firebaseAuth.currentUser;

      // For testing purposes, allow access to test user data even if not authenticated as that user
      const testUserId = 'Bbb0ezu0zeQqCDHghn3LLGXceM93';
      final isTestUser = userId == testUserId;

      if (user == null && !isTestUser) {
        throw Exception('User not authenticated');
      }

      // Allow access to test user data or ensure the requested userId matches the current user
      if (!isTestUser && user != null && userId != user.uid) {
        throw Exception('Access denied: Cannot access other user\'s resumes');
      }

      final querySnapshot = await _firestore
          .collection('resumes')
          .where('userId', isEqualTo: userId)
          .get();

      // For listing resumes, we only need basic info (no subcollections)
      // This improves performance for the resume list view
      final resumes = <ResumeModel>[];

      for (final doc in querySnapshot.docs) {
        final data = doc.data();

        // Add empty arrays for subcollections to satisfy the model
        final resumeData = {
          ...data,
          'workExperience': <Map<String, dynamic>>[],
          'education': <Map<String, dynamic>>[],
          'projects': <Map<String, dynamic>>[],
          'skills': <Map<String, dynamic>>[],
          'languages': <Map<String, dynamic>>[],
          'certifications': <Map<String, dynamic>>[],
          'socialMedia': <Map<String, dynamic>>[],
        };

        resumes.add(ResumeModel.fromJson(resumeData));
      }

      return resumes;
    } catch (e) {
      throw Exception('Failed to get user resumes: $e');
    }
  }

  Future<void> saveResume(ResumeModel resume) async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      final updatedResume = resume.copyWith(updatedAt: DateTime.now());

      // Save main resume document (without subcollection arrays)
      final mainResumeData = {
        'id': updatedResume.id,
        'userId': user.uid,
        'personalInfo': updatedResume.personalInfo.toJson(),
        'summary': updatedResume.summary,
        'createdAt': updatedResume.createdAt.toIso8601String(),
        'updatedAt': updatedResume.updatedAt.toIso8601String(),
      };

      await _firestore
          .collection('resumes')
          .doc(updatedResume.id)
          .set(mainResumeData, SetOptions(merge: true));

      // Save subcollections
      await _saveSubcollections(updatedResume);

      // Ensure user document exists and has this resume in their list
      await _ensureUserHasResume(user.uid, updatedResume);

      // Resume saved successfully
    } catch (e) {
      // Log error for debugging
      throw Exception('Failed to save resume: $e');
    }
  }

  Future<void> deleteResume(String resumeId) async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      // First verify the resume belongs to the current user
      final doc = await _firestore
          .collection('resumes')
          .doc(resumeId)
          .get();

      if (!doc.exists) {
        throw Exception('Resume not found');
      }

      final data = doc.data()!;
      if (data['userId'] != user.uid) {
        throw Exception('Access denied: Resume does not belong to current user');
      }

      // Delete the resume
      await _firestore
          .collection('resumes')
          .doc(resumeId)
          .delete();
    } catch (e) {
      throw Exception('Failed to delete resume: $e');
    }
  }

  Future<ResumeModel> duplicateResume(String resumeId) async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      // Get the original resume
      final originalResume = await getResume(resumeId);
      final newResumeId = _uuid.v4();

      // Create duplicated resume with new ID and timestamps
      final duplicatedResume = originalResume.copyWith(
        id: newResumeId,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // Save the duplicated resume to Firestore
      await saveResume(duplicatedResume);

      return duplicatedResume;
    } catch (e) {
      throw Exception('Failed to duplicate resume: $e');
    }
  }

  Future<String> shareResume(String resumeId) async {
    try {
      final user = _firebaseAuth.currentUser;
      if (user == null) {
        throw Exception('User not authenticated');
      }

      // Create a public share link
      final shareData = {
        'resumeId': resumeId,
        'userId': user.uid,
        'createdAt': DateTime.now().toIso8601String(),
        'isPublic': true,
      };

      final shareDoc = await _firestore
          .collection('shared_resumes')
          .add(shareData);

      // Return the share URL
      return 'https://your-app-domain.com/shared/${shareDoc.id}';
    } catch (e) {
      throw Exception('Failed to share resume: $e');
    }
  }

  /// Helper method to get subcollection data
  Future<List<Map<String, dynamic>>> _getSubcollection(String resumeId, String collectionName) async {
    try {
      final querySnapshot = await _firestore
          .collection('resumes')
          .doc(resumeId)
          .collection(collectionName)
          .get();

      return querySnapshot.docs.map((doc) => doc.data()).toList();
    } catch (e) {
      // Return empty list if subcollection doesn't exist or has errors
      return [];
    }
  }

  /// Helper method to save subcollections
  Future<void> _saveSubcollections(ResumeModel resume) async {
    final batch = _firestore.batch();

    // Clear existing subcollections and add new data
    final subcollections = [
      {'name': 'workExperience', 'data': resume.workExperience.map((e) => e.toJson()).toList()},
      {'name': 'education', 'data': resume.education.map((e) => e.toJson()).toList()},
      {'name': 'projects', 'data': resume.projects.map((e) => e.toJson()).toList()},
      {'name': 'skills', 'data': resume.skills.map((e) => e.toJson()).toList()},
      {'name': 'languages', 'data': resume.languages.map((e) => e.toJson()).toList()},
      {'name': 'certifications', 'data': resume.certifications.map((e) => e.toJson()).toList()},
      {'name': 'socialMedia', 'data': resume.socialMedia.map((e) => e.toJson()).toList()},
    ];

    for (final subcollection in subcollections) {
      final collectionRef = _firestore
          .collection('resumes')
          .doc(resume.id)
          .collection(subcollection['name'] as String);

      // Delete existing documents in this subcollection
      final existingDocs = await collectionRef.get();
      for (final doc in existingDocs.docs) {
        batch.delete(doc.reference);
      }

      // Add new documents
      final data = subcollection['data'] as List<Map<String, dynamic>>;
      for (final item in data) {
        final docRef = collectionRef.doc(item['id'] as String);
        batch.set(docRef, item);
      }
    }

    await batch.commit();
  }

  /// Helper method to ensure user document exists and has the resume in their list
  Future<void> _ensureUserHasResume(String userId, ResumeModel resume) async {
    try {
      final userRef = _firestore.collection('users').doc(userId);
      final userDoc = await userRef.get();

      if (!userDoc.exists) {
        // Create new user document
        final userData = {
          'id': userId,
          'email': resume.personalInfo.email,
          'displayName': '${resume.personalInfo.firstName} ${resume.personalInfo.lastName}',
          'photoUrl': resume.personalInfo.profileImageUrl,
          'emailVerified': true,
          'createdAt': DateTime.now().toIso8601String(),
          'lastSignIn': DateTime.now().toIso8601String(),
          'resumeIds': [resume.id],
        };

        await userRef.set(userData);
      } else {
        // Update existing user document to include this resume
        await userRef.update({
          'resumeIds': FieldValue.arrayUnion([resume.id]),
          'lastSignIn': DateTime.now().toIso8601String(),
        });
      }
    } catch (e) {
      // Log error but don't fail the resume save operation
      print('Warning: Failed to update user document: $e');
    }
  }
}
