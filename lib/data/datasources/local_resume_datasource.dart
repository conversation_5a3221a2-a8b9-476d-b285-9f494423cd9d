import 'package:hive/hive.dart';
import '../models/simple_resume_model.dart';
import '../../core/constants/app_constants.dart';

class LocalResumeDataSource {
  Future<Box> _getResumeBox() async {
    return await Hive.openBox(AppConstants.resumeBox);
  }

  Future<void> saveResume(ResumeModel resume) async {
    try {
      final box = await _getResumeBox();

      // Save the current resume
      await box.put(AppConstants.resumeDataKey, resume.toJson());

      // Also save to a list of all resumes for the user
      final existingResumes = await getAllResumes();
      final updatedResumes = existingResumes.where((r) => r.id != resume.id).toList();
      updatedResumes.add(resume);

      await box.put('all_resumes', updatedResumes.map((r) => r.toJson()).toList());
    } catch (e) {
      throw Exception('Failed to save resume locally: $e');
    }
  }

  Future<ResumeModel?> getResume() async {
    try {
      final box = await _getResumeBox();
      final resumeData = box.get(AppConstants.resumeDataKey);

      if (resumeData != null) {
        return ResumeModel.fromJson(Map<String, dynamic>.from(resumeData));
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get local resume: $e');
    }
  }

  Future<List<ResumeModel>> getAllResumes() async {
    try {
      final box = await _getResumeBox();
      final resumesData = box.get('all_resumes');

      if (resumesData != null) {
        final List<dynamic> resumesList = resumesData;
        return resumesList
            .map((data) => ResumeModel.fromJson(Map<String, dynamic>.from(data)))
            .toList();
      }
      return [];
    } catch (e) {
      throw Exception('Failed to get local resumes: $e');
    }
  }

  Future<void> deleteResume() async {
    try {
      final box = await _getResumeBox();
      await box.delete(AppConstants.resumeDataKey);
    } catch (e) {
      throw Exception('Failed to delete local resume: $e');
    }
  }

  Future<void> clearAllData() async {
    try {
      final box = await _getResumeBox();
      await box.clear();
    } catch (e) {
      throw Exception('Failed to clear local data: $e');
    }
  }
}
