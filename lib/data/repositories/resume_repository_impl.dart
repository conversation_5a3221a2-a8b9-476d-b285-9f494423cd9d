import '../../domain/repositories/resume_repository.dart';
import '../models/simple_resume_model.dart';
import '../datasources/firebase_resume_datasource.dart';
import '../datasources/local_resume_datasource.dart';
import '../datasources/pdf_export_datasource.dart';

class ResumeRepositoryImpl implements ResumeRepository {
  final FirebaseResumeDataSource _firebaseDataSource;
  final LocalResumeDataSource _localDataSource;
  final PdfExportDataSource _pdfExportDataSource;

  ResumeRepositoryImpl(
    this._firebaseDataSource,
    this._localDataSource,
    this._pdfExportDataSource,
  );

  @override
  Future<ResumeModel> getResume(String resumeId) async {
    try {
      return await _firebaseDataSource.getResume(resumeId);
    } catch (e) {
      // Fallback to local storage if Firebase fails
      final localResume = await _localDataSource.getResume();
      if (localResume != null && localResume.id == resumeId) {
        return localResume;
      }
      rethrow;
    }
  }

  @override
  Future<List<ResumeModel>> getUserResumes(String userId) async {
    try {
      return await _firebaseDataSource.getUserResumes(userId);
    } catch (e) {
      // Fallback to local storage if Firebase fails
      return await _localDataSource.getAllResumes();
    }
  }

  @override
  Future<void> saveResume(ResumeModel resume) async {
    // Always save locally first for immediate feedback
    await _localDataSource.saveResume(resume);

    try {
      // Then try to save to Firebase
      await _firebaseDataSource.saveResume(resume);
    } catch (e) {
      // If Firebase fails, the local save still succeeded
      // The data will sync when connection is restored
      throw Exception('Saved locally, but failed to sync with cloud: $e');
    }
  }

  @override
  Future<void> deleteResume(String resumeId) async {
    await _firebaseDataSource.deleteResume(resumeId);
  }

  @override
  Future<ResumeModel> duplicateResume(String resumeId) async {
    return await _firebaseDataSource.duplicateResume(resumeId);
  }

  @override
  Future<void> exportToPdf(ResumeModel resume) async {
    return await _pdfExportDataSource.exportToPdf(resume);
  }

  @override
  Future<String> shareResume(String resumeId) async {
    return await _firebaseDataSource.shareResume(resumeId);
  }

  @override
  Future<void> saveResumeLocally(ResumeModel resume) async {
    return await _localDataSource.saveResume(resume);
  }

  @override
  Future<ResumeModel?> getLocalResume() async {
    return await _localDataSource.getResume();
  }
}
