{"name": "resume-builder-test-data", "version": "1.0.0", "description": "Scripts to upload test data to Firebase for Resume Builder app", "main": "upload_test_data.js", "scripts": {"setup": "node setup.js", "check": "node check-firebase.js", "upload-test-data": "node upload_test_data.js", "upload-multiple": "node upload_multiple_resumes.js", "migrate": "node migrate_to_subcollections.js", "migrate-cleanup": "node migrate_to_subcollections.js --cleanup", "verify-links": "node verify_user_resume_links.js", "fix-links": "node verify_user_resume_links.js --fix"}, "dependencies": {"firebase-admin": "^12.0.0", "uuid": "^9.0.0"}, "devDependencies": {}, "keywords": ["firebase", "firestore", "test-data", "resume-builder"], "author": "Resume Builder Team", "license": "MIT"}