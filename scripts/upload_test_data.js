// Firebase Admin SDK script to upload test resume data to Firestore
// Run this script with: node scripts/upload_test_data.js

const admin = require('firebase-admin');
const { v4: uuidv4 } = require('uuid');

// Initialize Firebase Admin SDK
// Option 1: Using service account key file (recommended for local testing)
let serviceAccount;
try {
  serviceAccount = require('./serviceAccountKey.json');
  admin.initializeApp({
    credential: admin.credential.cert(serviceAccount),
    projectId: serviceAccount.project_id // Use project ID from service account
  });
  console.log('✅ Initialized with service account key file');
} catch (error) {
  console.log('⚠️  Service account key file not found. Trying environment variable...');

  // Option 2: Using environment variable (alternative method)
  if (process.env.GOOGLE_APPLICATION_CREDENTIALS) {
    admin.initializeApp({
      projectId: process.env.FIREBASE_PROJECT_ID || 'resume-d24cb'
    });
    console.log('✅ Initialized with environment variable');
  } else {
    console.error('❌ Firebase initialization failed!');
    console.error('Please either:');
    console.error('1. Download serviceAccountKey.json from Firebase Console, or');
    console.error('2. Set GOOGLE_APPLICATION_CREDENTIALS environment variable');
    console.error('\nSee setup instructions in README.md');
    process.exit(1);
  }
}

const db = admin.firestore();

// Test user ID - replace with actual user ID from your Firebase Auth
const TEST_USER_ID = 'nxZjGYfYUu3CMWY2278d';

// Sample resume data with proper CV information
const sampleResumeData = {
  id: uuidv4(),
  userId: TEST_USER_ID,
  personalInfo: {
    firstName: 'Sarah',
    lastName: 'Johnson',
    email: '<EMAIL>',
    phone: '+****************',
    address: '123 Tech Street',
    city: 'San Francisco',
    state: 'California',
    zipCode: '94105',
    country: 'United States',
    profileImageUrl: null
  },
  summary: 'Experienced Full-Stack Developer with 5+ years of expertise in mobile and web application development. Proven track record of delivering scalable solutions using modern technologies including Flutter, React, and Node.js. Passionate about creating user-centric applications and leading development teams to success.',
  workExperience: [
    {
      id: uuidv4(),
      jobTitle: 'Senior Mobile Developer',
      company: 'TechCorp Solutions',
      location: 'San Francisco, CA',
      startDate: '2022-03-01T00:00:00.000Z',
      endDate: null,
      isCurrentJob: true,
      description: 'Lead mobile development team in creating cross-platform applications using Flutter and React Native.',
      achievements: [
        'Developed and launched 3 mobile apps with 100K+ downloads each',
        'Reduced app crash rate by 40% through improved error handling',
        'Mentored 4 junior developers and established code review processes',
        'Implemented CI/CD pipeline reducing deployment time by 60%'
      ]
    },
    {
      id: uuidv4(),
      jobTitle: 'Full-Stack Developer',
      company: 'StartupXYZ',
      location: 'Palo Alto, CA',
      startDate: '2020-01-15T00:00:00.000Z',
      endDate: '2022-02-28T00:00:00.000Z',
      isCurrentJob: false,
      description: 'Developed web applications using React, Node.js, and MongoDB. Collaborated with design team to implement responsive user interfaces.',
      achievements: [
        'Built MVP that secured $2M in Series A funding',
        'Optimized database queries improving response time by 50%',
        'Implemented real-time chat feature using WebSocket',
        'Maintained 99.9% uptime for production applications'
      ]
    },
    {
      id: uuidv4(),
      jobTitle: 'Junior Software Developer',
      company: 'Digital Innovations Inc',
      location: 'San Jose, CA',
      startDate: '2019-06-01T00:00:00.000Z',
      endDate: '2019-12-31T00:00:00.000Z',
      isCurrentJob: false,
      description: 'Developed and maintained web applications using JavaScript, HTML, CSS, and PHP. Participated in agile development processes.',
      achievements: [
        'Contributed to 5 major feature releases',
        'Fixed 50+ bugs improving application stability',
        'Implemented automated testing reducing manual testing time by 30%',
        'Collaborated with cross-functional teams of 8+ members'
      ]
    }
  ],
  education: [
    {
      id: uuidv4(),
      degree: 'Bachelor of Science in Computer Science',
      institution: 'Stanford University',
      location: 'Stanford, CA',
      startDate: '2015-09-01T00:00:00.000Z',
      endDate: '2019-05-15T00:00:00.000Z',
      isCurrentlyStudying: false,
      gpa: '3.8',
      description: 'Specialized in Software Engineering and Human-Computer Interaction. Dean\'s List for 6 semesters.'
    },
    {
      id: uuidv4(),
      degree: 'Master of Science in Software Engineering',
      institution: 'UC Berkeley',
      location: 'Berkeley, CA',
      startDate: '2019-09-01T00:00:00.000Z',
      endDate: '2021-05-15T00:00:00.000Z',
      isCurrentlyStudying: false,
      gpa: '3.9',
      description: 'Focus on Mobile Computing and Distributed Systems. Graduate Research Assistant.'
    }
  ],
  projects: [
    {
      id: uuidv4(),
      name: 'EcoTracker Mobile App',
      description: 'A Flutter-based mobile application that helps users track their carbon footprint and suggests eco-friendly alternatives.',
      technologies: ['Flutter', 'Dart', 'Firebase', 'Google Maps API', 'REST APIs'],
      projectUrl: 'https://ecotracker-app.com',
      githubUrl: 'https://github.com/sarahjohnson/ecotracker',
      startDate: '2023-01-01T00:00:00.000Z',
      endDate: '2023-06-30T00:00:00.000Z',
      achievements: [
        'Featured in Google Play Store\'s "Apps for Good" section',
        '10,000+ active users within first 3 months',
        'Integrated with 5 environmental data APIs',
        'Achieved 4.8/5 star rating on app stores'
      ]
    },
    {
      id: uuidv4(),
      name: 'TaskFlow Web Platform',
      description: 'A comprehensive project management web application built with React and Node.js, featuring real-time collaboration and advanced analytics.',
      technologies: ['React', 'Node.js', 'MongoDB', 'Socket.io', 'Chart.js', 'AWS'],
      projectUrl: 'https://taskflow-platform.com',
      githubUrl: 'https://github.com/sarahjohnson/taskflow',
      startDate: '2022-08-01T00:00:00.000Z',
      endDate: '2022-12-15T00:00:00.000Z',
      achievements: [
        'Supports 1000+ concurrent users',
        'Reduced project completion time by 25% for beta users',
        'Implemented advanced data visualization features',
        'Deployed on AWS with auto-scaling capabilities'
      ]
    }
  ],
  skills: [
    {
      id: uuidv4(),
      category: 'Programming Languages',
      skills: [
        { id: uuidv4(), name: 'Dart', proficiencyLevel: 5 },
        { id: uuidv4(), name: 'JavaScript', proficiencyLevel: 5 },
        { id: uuidv4(), name: 'TypeScript', proficiencyLevel: 4 },
        { id: uuidv4(), name: 'Python', proficiencyLevel: 4 },
        { id: uuidv4(), name: 'Java', proficiencyLevel: 3 }
      ]
    },
    {
      id: uuidv4(),
      category: 'Frameworks & Libraries',
      skills: [
        { id: uuidv4(), name: 'Flutter', proficiencyLevel: 5 },
        { id: uuidv4(), name: 'React', proficiencyLevel: 5 },
        { id: uuidv4(), name: 'Node.js', proficiencyLevel: 4 },
        { id: uuidv4(), name: 'Express.js', proficiencyLevel: 4 },
        { id: uuidv4(), name: 'React Native', proficiencyLevel: 4 }
      ]
    },
    {
      id: uuidv4(),
      category: 'Databases & Cloud',
      skills: [
        { id: uuidv4(), name: 'Firebase', proficiencyLevel: 5 },
        { id: uuidv4(), name: 'MongoDB', proficiencyLevel: 4 },
        { id: uuidv4(), name: 'PostgreSQL', proficiencyLevel: 4 },
        { id: uuidv4(), name: 'AWS', proficiencyLevel: 3 },
        { id: uuidv4(), name: 'Google Cloud', proficiencyLevel: 3 }
      ]
    }
  ],
  languages: [
    {
      id: uuidv4(),
      language: 'English',
      proficiency: 'Native'
    },
    {
      id: uuidv4(),
      language: 'Spanish',
      proficiency: 'Intermediate'
    },
    {
      id: uuidv4(),
      language: 'Mandarin',
      proficiency: 'Beginner'
    }
  ],
  certifications: [
    {
      id: uuidv4(),
      name: 'Google Associate Cloud Engineer',
      issuer: 'Google Cloud',
      issueDate: '2023-03-15T00:00:00.000Z',
      expiryDate: '2026-03-15T00:00:00.000Z',
      credentialId: 'GCP-ACE-2023-001234',
      credentialUrl: 'https://cloud.google.com/certification/cloud-engineer'
    },
    {
      id: uuidv4(),
      name: 'AWS Certified Developer - Associate',
      issuer: 'Amazon Web Services',
      issueDate: '2022-11-20T00:00:00.000Z',
      expiryDate: '2025-11-20T00:00:00.000Z',
      credentialId: 'AWS-DEV-2022-567890',
      credentialUrl: 'https://aws.amazon.com/certification/certified-developer-associate/'
    },
    {
      id: uuidv4(),
      name: 'Flutter Certified Developer',
      issuer: 'Google Developers',
      issueDate: '2023-01-10T00:00:00.000Z',
      expiryDate: null,
      credentialId: 'FLUTTER-DEV-2023-112233',
      credentialUrl: 'https://developers.google.com/certification/flutter'
    }
  ],
  socialMedia: [
    {
      id: uuidv4(),
      platform: 'LinkedIn',
      url: 'https://linkedin.com/in/sarah-johnson-dev',
      username: 'sarah-johnson-dev'
    },
    {
      id: uuidv4(),
      platform: 'GitHub',
      url: 'https://github.com/sarahjohnson',
      username: 'sarahjohnson'
    },
    {
      id: uuidv4(),
      platform: 'Twitter',
      url: 'https://twitter.com/sarahdev',
      username: 'sarahdev'
    }
  ],
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString()
};

// Function to upload the resume data using separate collections
async function uploadTestResumeData() {
  try {
    console.log('Starting to upload test resume data...');

    // First, create or update the user document
    await createOrUpdateUser(TEST_USER_ID, sampleResumeData);

    // Upload the main resume document
    await db.collection('resumes').doc(sampleResumeData.id).set({
      id: sampleResumeData.id,
      userId: sampleResumeData.userId,
      personalInfo: sampleResumeData.personalInfo,
      summary: sampleResumeData.summary,
      createdAt: sampleResumeData.createdAt,
      updatedAt: sampleResumeData.updatedAt
    });

    // Upload work experience as separate collection
    if (sampleResumeData.workExperience.length > 0) {
      const workExpBatch = db.batch();
      sampleResumeData.workExperience.forEach(exp => {
        const docRef = db.collection('resumes').doc(sampleResumeData.id).collection('workExperience').doc(exp.id);
        workExpBatch.set(docRef, exp);
      });
      await workExpBatch.commit();
      console.log(`   ✅ Uploaded ${sampleResumeData.workExperience.length} work experiences`);
    }

    // Upload education as separate collection
    if (sampleResumeData.education.length > 0) {
      const educationBatch = db.batch();
      sampleResumeData.education.forEach(edu => {
        const docRef = db.collection('resumes').doc(sampleResumeData.id).collection('education').doc(edu.id);
        educationBatch.set(docRef, edu);
      });
      await educationBatch.commit();
      console.log(`   ✅ Uploaded ${sampleResumeData.education.length} education entries`);
    }

    // Upload projects as separate collection
    if (sampleResumeData.projects.length > 0) {
      const projectsBatch = db.batch();
      sampleResumeData.projects.forEach(project => {
        const docRef = db.collection('resumes').doc(sampleResumeData.id).collection('projects').doc(project.id);
        projectsBatch.set(docRef, project);
      });
      await projectsBatch.commit();
      console.log(`   ✅ Uploaded ${sampleResumeData.projects.length} projects`);
    }

    // Upload skills as separate collection
    if (sampleResumeData.skills.length > 0) {
      const skillsBatch = db.batch();
      sampleResumeData.skills.forEach(skillCategory => {
        const docRef = db.collection('resumes').doc(sampleResumeData.id).collection('skills').doc(skillCategory.id);
        skillsBatch.set(docRef, skillCategory);
      });
      await skillsBatch.commit();
      console.log(`   ✅ Uploaded ${sampleResumeData.skills.length} skill categories`);
    }

    // Upload languages as separate collection
    if (sampleResumeData.languages.length > 0) {
      const languagesBatch = db.batch();
      sampleResumeData.languages.forEach(language => {
        const docRef = db.collection('resumes').doc(sampleResumeData.id).collection('languages').doc(language.id);
        languagesBatch.set(docRef, language);
      });
      await languagesBatch.commit();
      console.log(`   ✅ Uploaded ${sampleResumeData.languages.length} languages`);
    }

    // Upload certifications as separate collection
    if (sampleResumeData.certifications.length > 0) {
      const certificationsBatch = db.batch();
      sampleResumeData.certifications.forEach(cert => {
        const docRef = db.collection('resumes').doc(sampleResumeData.id).collection('certifications').doc(cert.id);
        certificationsBatch.set(docRef, cert);
      });
      await certificationsBatch.commit();
      console.log(`   ✅ Uploaded ${sampleResumeData.certifications.length} certifications`);
    }

    // Upload social media as separate collection
    if (sampleResumeData.socialMedia.length > 0) {
      const socialMediaBatch = db.batch();
      sampleResumeData.socialMedia.forEach(social => {
        const docRef = db.collection('resumes').doc(sampleResumeData.id).collection('socialMedia').doc(social.id);
        socialMediaBatch.set(docRef, social);
      });
      await socialMediaBatch.commit();
      console.log(`   ✅ Uploaded ${sampleResumeData.socialMedia.length} social media profiles`);
    }

    // Update user document with the new resume ID
    await addResumeToUser(TEST_USER_ID, sampleResumeData.id);

    console.log('\n🎉 Successfully uploaded test resume data with separate collections!');
    console.log(`Resume ID: ${sampleResumeData.id}`);
    console.log(`User ID: ${TEST_USER_ID}`);
    console.log(`✅ User document updated with resume reference`);
    console.log('You can now test the app with this data.');

  } catch (error) {
    console.error('❌ Error uploading test data:', error.message);

    // Provide specific guidance for common errors
    if (error.message.includes('Cloud Firestore API has not been used')) {
      console.error('\n🔧 SOLUTION:');
      console.error('1. Go to Firebase Console: https://console.firebase.google.com/');
      console.error('2. Select your project');
      console.error('3. Go to "Firestore Database" in the sidebar');
      console.error('4. Click "Create database"');
      console.error('5. Choose "Start in test mode"');
      console.error('6. Select a location and create');
      console.error('7. Wait a few minutes, then retry this script');
    } else if (error.message.includes('PERMISSION_DENIED')) {
      console.error('\n🔧 SOLUTION:');
      console.error('1. Check your Firebase project ID is correct');
      console.error('2. Verify your service account key has Firestore permissions');
      console.error('3. Make sure Firestore is enabled in your project');
    } else if (error.message.includes('User not authenticated')) {
      console.error('\n🔧 SOLUTION:');
      console.error('1. Make sure the TEST_USER_ID exists in Firebase Auth');
      console.error('2. Create a user account in your app first');
      console.error('3. Copy the User UID from Firebase Console → Authentication');
    }

    throw error;
  }
}

// Helper function to create or update user document
async function createOrUpdateUser(userId, resumeData) {
  try {
    const userRef = db.collection('users').doc(userId);
    const userDoc = await userRef.get();

    if (userDoc.exists) {
      console.log('   📝 User document already exists, will update resume list');
    } else {
      // Create new user document
      const userData = {
        id: userId,
        email: resumeData.personalInfo.email,
        displayName: `${resumeData.personalInfo.firstName} ${resumeData.personalInfo.lastName}`,
        photoUrl: resumeData.personalInfo.profileImageUrl || null,
        emailVerified: true, // Assume verified for test data
        createdAt: new Date().toISOString(),
        lastSignIn: new Date().toISOString(),
        resumeIds: [] // Will be updated separately
      };

      await userRef.set(userData);
      console.log('   ✅ Created new user document');
    }
  } catch (error) {
    console.error('   ❌ Failed to create/update user:', error.message);
    throw error;
  }
}

// Helper function to add resume ID to user's resume list
async function addResumeToUser(userId, resumeId) {
  try {
    const userRef = db.collection('users').doc(userId);

    // Use arrayUnion to add resume ID if it doesn't already exist
    await userRef.update({
      resumeIds: admin.firestore.FieldValue.arrayUnion(resumeId),
      lastSignIn: new Date().toISOString() // Update last activity
    });

    console.log('   ✅ Added resume to user\'s resume list');
  } catch (error) {
    console.error('   ❌ Failed to add resume to user:', error.message);
    throw error;
  }
}

// Run the upload function
uploadTestResumeData()
  .then(() => {
    console.log('\nScript completed successfully!');
    process.exit(0);
  })
  .catch((error) => {
    console.error('Script failed:', error);
    process.exit(1);
  });
