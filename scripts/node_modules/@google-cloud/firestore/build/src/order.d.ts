/*!
 * Copyright 2017 Google Inc. All Rights Reserved.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
import { google } from '../protos/firestore_v1_proto_api';
import api = google.firestore.v1;
/*!
 * @private
 * @internal
 */
export declare function primitiveComparator(left: string | boolean | number, right: string | boolean | number): number;
/*!
 * @private
 * @internal
 */
export declare function compareArrays(left: api.IValue[], right: api.IValue[]): number;
/*!
 * Compare strings in UTF-8 encoded byte order
 * @private
 * @internal
 */
export declare function compareUtf8Strings(left: string, right: string): number;
/*!
 * @private
 * @internal
 */
export declare function compare(left: api.IValue, right: api.IValue): number;
