// Protocol Buffers - Google's data interchange format
// Copyright 2023 Google Inc.  All rights reserved.
//
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file or at
// https://developers.google.com/open-source/licenses/bsd

syntax = "proto3";

package protobuf_editions_test.proto3;

message Proto3Implicit {
  int32 int32_field = 17;
  float float_field = 18;
  double double_field = 19;
  int64 int64_field = 20;
  uint32 uint32_field = 21;
  uint64 uint64_field = 22;
  sint32 sint32_field = 23;
  sint64 sint64_field = 24;
  fixed32 fixed32_field = 25;
  fixed64 fixed64_field = 26;
  sfixed32 sfixed32_field = 27;
  sfixed64 sfixed64_field = 28;
  bool bool_field = 29;
  string string_field = 30;
  bytes bytes_field = 31;

  message SubMessage {
    int32 int32_field = 17;
    float float_field = 18;
    double double_field = 19;
    int64 int64_field = 20;
    uint32 uint32_field = 21;
    uint64 uint64_field = 22;
    sint32 sint32_field = 23;
    sint64 sint64_field = 24;
    fixed32 fixed32_field = 25;
    fixed64 fixed64_field = 26;
    sfixed32 sfixed32_field = 27;
    sfixed64 sfixed64_field = 28;
    bool bool_field = 29;
    string string_field = 30;
    bytes bytes = 31;
  }

  oneof oneof_field {
    int32 int32_oneof_field = 152;
    float float_oneof_field = 153;
    double double_oneof_field = 154;
    int64 int64_oneof_field = 155;
    uint32 uint32_oneof_field = 156;
    uint64 uint64_oneof_field = 157;
    sint32 sint32_oneof_field = 158;
    sint64 sint64_oneof_field = 159;
    fixed32 fixed32_oneof_field = 160;
    fixed64 fixed64_oneof_field = 161;
    sfixed32 sfixed32_oneof_field = 162;
    sfixed64 sfixed64_oneof_field = 163;
    bool bool_oneof_field = 164;
    string string_oneof_field = 165;
    bytes bytes_oneof_field = 166;
  }
  SubMessage sub_message = 2;
}
