// Protocol Buffers - Google's data interchange format
// Copyright 2023 Google Inc.  All rights reserved.
//
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file or at
// https://developers.google.com/open-source/licenses/bsd

syntax = "proto3";

package protobuf_editions_test.proto3;

message Proto3Unpacked {
  repeated int32 int32_field = 1 [packed = false];
  repeated string string_field = 2 [packed = false];
  message SubMessage {
    int32 int32_field = 1;
  }
  repeated SubMessage sub_message_field = 3 [packed = false];
}
