{"version": 3, "file": "consoleLogger.js", "sourceRoot": "", "sources": ["../../../src/diag/consoleLogger.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAKH,MAAM,UAAU,GAAiD;IAC/D,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,OAAO,EAAE;IAC1B,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE;IACxB,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE;IACxB,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,OAAO,EAAE;IAC1B,EAAE,CAAC,EAAE,SAAS,EAAE,CAAC,EAAE,OAAO,EAAE;CAC7B,CAAC;AAEF;;;;GAIG;AACH,MAAM,OAAO,iBAAiB;IAC5B;QACE,SAAS,YAAY,CAAC,QAAwB;YAC5C,OAAO,UAAU,GAAG,IAAI;gBACtB,IAAI,OAAO,EAAE;oBACX,mFAAmF;oBACnF,sCAAsC;oBACtC,IAAI,OAAO,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;oBAChC,IAAI,OAAO,OAAO,KAAK,UAAU,EAAE;wBACjC,6CAA6C;wBAC7C,sCAAsC;wBACtC,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC;qBACvB;oBAED,uBAAuB;oBACvB,IAAI,OAAO,OAAO,KAAK,UAAU,EAAE;wBACjC,OAAO,OAAO,CAAC,KAAK,CAAC,OAAO,EAAE,IAAI,CAAC,CAAC;qBACrC;iBACF;YACH,CAAC,CAAC;QACJ,CAAC;QAED,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YAC1C,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAG,YAAY,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;SACvD;IACH,CAAC;CAkCF", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { DiagLogger, DiagLogFunction } from './types';\n\ntype ConsoleMapKeys = 'error' | 'warn' | 'info' | 'debug' | 'trace';\nconst consoleMap: { n: keyof DiagLogger; c: ConsoleMapKeys }[] = [\n  { n: 'error', c: 'error' },\n  { n: 'warn', c: 'warn' },\n  { n: 'info', c: 'info' },\n  { n: 'debug', c: 'debug' },\n  { n: 'verbose', c: 'trace' },\n];\n\n/**\n * A simple Immutable Console based diagnostic logger which will output any messages to the Console.\n * If you want to limit the amount of logging to a specific level or lower use the\n * {@link createLogLevelDiagLogger}\n */\nexport class DiagConsoleLogger implements DiagLogger {\n  constructor() {\n    function _consoleFunc(funcName: ConsoleMapKeys): DiagLogFunction {\n      return function (...args) {\n        if (console) {\n          // Some environments only expose the console when the F12 developer console is open\n          // eslint-disable-next-line no-console\n          let theFunc = console[funcName];\n          if (typeof theFunc !== 'function') {\n            // Not all environments support all functions\n            // eslint-disable-next-line no-console\n            theFunc = console.log;\n          }\n\n          // One last final check\n          if (typeof theFunc === 'function') {\n            return theFunc.apply(console, args);\n          }\n        }\n      };\n    }\n\n    for (let i = 0; i < consoleMap.length; i++) {\n      this[consoleMap[i].n] = _consoleFunc(consoleMap[i].c);\n    }\n  }\n\n  /** Log an error scenario that was not expected and caused the requested operation to fail. */\n  public error!: DiagLogFunction;\n\n  /**\n   * Log a warning scenario to inform the developer of an issues that should be investigated.\n   * The requested operation may or may not have succeeded or completed.\n   */\n  public warn!: DiagLogFunction;\n\n  /**\n   * Log a general informational message, this should not affect functionality.\n   * This is also the default logging level so this should NOT be used for logging\n   * debugging level information.\n   */\n  public info!: DiagLogFunction;\n\n  /**\n   * Log a general debug message that can be useful for identifying a failure.\n   * Information logged at this level may include diagnostic details that would\n   * help identify a failure scenario. Useful scenarios would be to log the execution\n   * order of async operations\n   */\n  public debug!: DiagLogFunction;\n\n  /**\n   * Log a detailed (verbose) trace level logging that can be used to identify failures\n   * where debug level logging would be insufficient, this level of tracing can include\n   * input and output parameters and as such may include PII information passing through\n   * the API. As such it is recommended that this level of tracing should not be enabled\n   * in a production environment.\n   */\n  public verbose!: DiagLogFunction;\n}\n"]}