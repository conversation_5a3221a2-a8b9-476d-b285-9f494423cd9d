interface ExceptionWithCode {
    code: string | number;
    name?: string;
    message?: string;
    stack?: string;
}
interface ExceptionWithMessage {
    code?: string | number;
    message: string;
    name?: string;
    stack?: string;
}
interface ExceptionWithName {
    code?: string | number;
    message?: string;
    name: string;
    stack?: string;
}
/**
 * Defines Exception.
 *
 * string or an object with one of (message or name or code) and optional stack
 */
export declare type Exception = ExceptionWithCode | ExceptionWithMessage | ExceptionWithName | string;
export {};
//# sourceMappingURL=Exception.d.ts.map