{"version": 3, "file": "trace_state.js", "sourceRoot": "", "sources": ["../../../src/trace/trace_state.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport interface TraceState {\n  /**\n   * Create a new TraceState which inherits from this TraceState and has the\n   * given key set.\n   * The new entry will always be added in the front of the list of states.\n   *\n   * @param key key of the TraceState entry.\n   * @param value value of the TraceState entry.\n   */\n  set(key: string, value: string): TraceState;\n\n  /**\n   * Return a new TraceState which inherits from this TraceState but does not\n   * contain the given key.\n   *\n   * @param key the key for the TraceState entry to be removed.\n   */\n  unset(key: string): TraceState;\n\n  /**\n   * Returns the value to which the specified key is mapped, or `undefined` if\n   * this map contains no mapping for the key.\n   *\n   * @param key with which the specified value is to be associated.\n   * @returns the value to which the specified key is mapped, or `undefined` if\n   *     this map contains no mapping for the key.\n   */\n  get(key: string): string | undefined;\n\n  // TODO: Consider to add support for merging an object as well by also\n  // accepting a single internalTraceState argument similar to the constructor.\n\n  /**\n   * Serializes the TraceState to a `list` as defined below. The `list` is a\n   * series of `list-members` separated by commas `,`, and a list-member is a\n   * key/value pair separated by an equals sign `=`. Spaces and horizontal tabs\n   * surrounding `list-members` are ignored. There can be a maximum of 32\n   * `list-members` in a `list`.\n   *\n   * @returns the serialized string.\n   */\n  serialize(): string;\n}\n"]}