{"version": 3, "file": "link.js", "sourceRoot": "", "sources": ["../../../src/trace/link.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { SpanAttributes } from './attributes';\nimport { SpanContext } from './span_context';\n\n/**\n * A pointer from the current {@link Span} to another span in the same trace or\n * in a different trace.\n * Few examples of Link usage.\n * 1. Batch Processing: A batch of elements may contain elements associated\n *    with one or more traces/spans. Since there can only be one parent\n *    SpanContext, Link is used to keep reference to SpanContext of all\n *    elements in the batch.\n * 2. Public Endpoint: A SpanContext in incoming client request on a public\n *    endpoint is untrusted from service provider perspective. In such case it\n *    is advisable to start a new trace with appropriate sampling decision.\n *    However, it is desirable to associate incoming SpanContext to new trace\n *    initiated on service provider side so two traces (from Client and from\n *    Service Provider) can be correlated.\n */\nexport interface Link {\n  /** The {@link SpanContext} of a linked span. */\n  context: SpanContext;\n  /** A set of {@link SpanAttributes} on the link. */\n  attributes?: SpanAttributes;\n  /** Count of attributes of the link that were dropped due to collection limits */\n  droppedAttributesCount?: number;\n}\n"]}