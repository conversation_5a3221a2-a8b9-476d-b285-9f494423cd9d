{"version": 3, "file": "ObservableResult.js", "sourceRoot": "", "sources": ["../../../src/metrics/ObservableResult.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { MetricAttributes, Observable } from './Metric';\n\n/**\n * Interface that is being used in callback function for Observable Metric.\n */\nexport interface ObservableResult<\n  AttributesTypes extends MetricAttributes = MetricAttributes,\n> {\n  /**\n   * Observe a measurement of the value associated with the given attributes.\n   *\n   * @param value The value to be observed.\n   * @param attributes The attributes associated with the value. If more than\n   * one values associated with the same attributes values, SDK may pick the\n   * last one or simply drop the entire observable result.\n   */\n  observe(\n    this: ObservableResult<AttributesTypes>,\n    value: number,\n    attributes?: AttributesTypes\n  ): void;\n}\n\n/**\n * Interface that is being used in batch observable callback function.\n */\nexport interface BatchObservableResult<\n  AttributesTypes extends MetricAttributes = MetricAttributes,\n> {\n  /**\n   * Observe a measurement of the value associated with the given attributes.\n   *\n   * @param metric The observable metric to be observed.\n   * @param value The value to be observed.\n   * @param attributes The attributes associated with the value. If more than\n   * one values associated with the same attributes values, SDK may pick the\n   * last one or simply drop the entire observable result.\n   */\n  observe(\n    this: BatchObservableResult<AttributesTypes>,\n    metric: Observable<AttributesTypes>,\n    value: number,\n    attributes?: AttributesTypes\n  ): void;\n}\n"]}