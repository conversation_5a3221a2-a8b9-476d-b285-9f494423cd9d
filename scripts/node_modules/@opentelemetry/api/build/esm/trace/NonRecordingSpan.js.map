{"version": 3, "file": "NonRecordingSpan.js", "sourceRoot": "", "sources": ["../../../src/trace/NonRecordingSpan.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAKH,OAAO,EAAE,oBAAoB,EAAE,MAAM,0BAA0B,CAAC;AAMhE;;;;GAIG;AACH;IACE,0BACmB,YAAgD;QAAhD,6BAAA,EAAA,mCAAgD;QAAhD,iBAAY,GAAZ,YAAY,CAAoC;IAChE,CAAC;IAEJ,yBAAyB;IACzB,sCAAW,GAAX;QACE,OAAO,IAAI,CAAC,YAAY,CAAC;IAC3B,CAAC;IAED,0BAA0B;IAC1B,uCAAY,GAAZ,UAAa,IAAY,EAAE,MAAe;QACxC,OAAO,IAAI,CAAC;IACd,CAAC;IAED,0BAA0B;IAC1B,wCAAa,GAAb,UAAc,WAA2B;QACvC,OAAO,IAAI,CAAC;IACd,CAAC;IAED,0BAA0B;IAC1B,mCAAQ,GAAR,UAAS,KAAa,EAAE,WAA4B;QAClD,OAAO,IAAI,CAAC;IACd,CAAC;IAED,kCAAO,GAAP,UAAQ,KAAW;QACjB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,mCAAQ,GAAR,UAAS,MAAc;QACrB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,0BAA0B;IAC1B,oCAAS,GAAT,UAAU,OAAmB;QAC3B,OAAO,IAAI,CAAC;IACd,CAAC;IAED,0BAA0B;IAC1B,qCAAU,GAAV,UAAW,KAAa;QACtB,OAAO,IAAI,CAAC;IACd,CAAC;IAED,0BAA0B;IAC1B,8BAAG,GAAH,UAAI,QAAoB,IAAS,CAAC;IAElC,yDAAyD;IACzD,sCAAW,GAAX;QACE,OAAO,KAAK,CAAC;IACf,CAAC;IAED,0BAA0B;IAC1B,0CAAe,GAAf,UAAgB,UAAqB,EAAE,KAAiB,IAAS,CAAC;IACpE,uBAAC;AAAD,CAAC,AArDD,IAqDC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { Exception } from '../common/Exception';\nimport { TimeInput } from '../common/Time';\nimport { SpanAttributes } from './attributes';\nimport { INVALID_SPAN_CONTEXT } from './invalid-span-constants';\nimport { Span } from './span';\nimport { SpanContext } from './span_context';\nimport { SpanStatus } from './status';\nimport { Link } from './link';\n\n/**\n * The NonRecordingSpan is the default {@link Span} that is used when no Span\n * implementation is available. All operations are no-op including context\n * propagation.\n */\nexport class NonRecordingSpan implements Span {\n  constructor(\n    private readonly _spanContext: SpanContext = INVALID_SPAN_CONTEXT\n  ) {}\n\n  // Returns a SpanContext.\n  spanContext(): SpanContext {\n    return this._spanContext;\n  }\n\n  // By default does nothing\n  setAttribute(_key: string, _value: unknown): this {\n    return this;\n  }\n\n  // By default does nothing\n  setAttributes(_attributes: SpanAttributes): this {\n    return this;\n  }\n\n  // By default does nothing\n  addEvent(_name: string, _attributes?: SpanAttributes): this {\n    return this;\n  }\n\n  addLink(_link: Link): this {\n    return this;\n  }\n\n  addLinks(_links: Link[]): this {\n    return this;\n  }\n\n  // By default does nothing\n  setStatus(_status: SpanStatus): this {\n    return this;\n  }\n\n  // By default does nothing\n  updateName(_name: string): this {\n    return this;\n  }\n\n  // By default does nothing\n  end(_endTime?: TimeInput): void {}\n\n  // isRecording always returns false for NonRecordingSpan.\n  isRecording(): boolean {\n    return false;\n  }\n\n  // By default does nothing\n  recordException(_exception: Exception, _time?: TimeInput): void {}\n}\n"]}