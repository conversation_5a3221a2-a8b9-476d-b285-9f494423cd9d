{"version": 3, "file": "metrics-api.js", "sourceRoot": "", "sources": ["../../src/metrics-api.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;AAEH,sEAAsE;AACtE,qCAAqC;AACrC,OAAO,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;AAC3C,iCAAiC;AACjC,MAAM,CAAC,IAAM,OAAO,GAAG,UAAU,CAAC,WAAW,EAAE,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n// Split module-level variable definition into separate files to allow\n// tree-shaking on each api instance.\nimport { MetricsAPI } from './api/metrics';\n/** Entrypoint for metrics API */\nexport const metrics = MetricsAPI.getInstance();\n"]}