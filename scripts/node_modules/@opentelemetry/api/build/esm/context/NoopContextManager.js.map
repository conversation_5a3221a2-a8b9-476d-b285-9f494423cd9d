{"version": 3, "file": "NoopContextManager.js", "sourceRoot": "", "sources": ["../../../src/context/NoopContextManager.ts"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;;GAcG;;;;;;;;;;;;;;;;;;;;;;;;;;AAEH,OAAO,EAAE,YAAY,EAAE,MAAM,WAAW,CAAC;AAGzC;IAAA;IAyBA,CAAC;IAxBC,mCAAM,GAAN;QACE,OAAO,YAAY,CAAC;IACtB,CAAC;IAED,iCAAI,GAAJ,UACE,QAAuB,EACvB,EAAK,EACL,OAA8B;QAC9B,cAAU;aAAV,UAAU,EAAV,qBAAU,EAAV,IAAU;YAAV,6BAAU;;QAEV,OAAO,EAAE,CAAC,IAAI,OAAP,EAAE,iBAAM,OAAO,UAAK,IAAI,WAAE;IACnC,CAAC;IAED,iCAAI,GAAJ,UAAQ,QAAuB,EAAE,MAAS;QACxC,OAAO,MAAM,CAAC;IAChB,CAAC;IAED,mCAAM,GAAN;QACE,OAAO,IAAI,CAAC;IACd,CAAC;IAED,oCAAO,GAAP;QACE,OAAO,IAAI,CAAC;IACd,CAAC;IACH,yBAAC;AAAD,CAAC,AAzBD,IAyBC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { ROOT_CONTEXT } from './context';\nimport * as types from './types';\n\nexport class NoopContextManager implements types.ContextManager {\n  active(): types.Context {\n    return ROOT_CONTEXT;\n  }\n\n  with<A extends unknown[], F extends (...args: A) => ReturnType<F>>(\n    _context: types.Context,\n    fn: F,\n    thisArg?: ThisParameterType<F>,\n    ...args: A\n  ): ReturnType<F> {\n    return fn.call(thisArg, ...args);\n  }\n\n  bind<T>(_context: types.Context, target: T): T {\n    return target;\n  }\n\n  enable(): this {\n    return this;\n  }\n\n  disable(): this {\n    return this;\n  }\n}\n"]}