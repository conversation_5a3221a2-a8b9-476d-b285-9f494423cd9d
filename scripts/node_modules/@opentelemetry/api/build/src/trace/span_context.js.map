{"version": 3, "file": "span_context.js", "sourceRoot": "", "sources": ["../../../src/trace/span_context.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nimport { TraceState } from './trace_state';\n\n/**\n * A SpanContext represents the portion of a {@link Span} which must be\n * serialized and propagated along side of a {@link Baggage}.\n */\nexport interface SpanContext {\n  /**\n   * The ID of the trace that this span belongs to. It is worldwide unique\n   * with practically sufficient probability by being made as 16 randomly\n   * generated bytes, encoded as a 32 lowercase hex characters corresponding to\n   * 128 bits.\n   */\n  traceId: string;\n  /**\n   * The ID of the Span. It is globally unique with practically sufficient\n   * probability by being made as 8 randomly generated bytes, encoded as a 16\n   * lowercase hex characters corresponding to 64 bits.\n   */\n  spanId: string;\n  /**\n   * Only true if the SpanContext was propagated from a remote parent.\n   */\n  isRemote?: boolean;\n  /**\n   * Trace flags to propagate.\n   *\n   * It is represented as 1 byte (bitmap). Bit to represent whether trace is\n   * sampled or not. When set, the least significant bit documents that the\n   * caller may have recorded trace data. A caller who does not record trace\n   * data out-of-band leaves this flag unset.\n   *\n   * see {@link TraceFlags} for valid flag values.\n   */\n  traceFlags: number;\n  /**\n   * Tracing-system-specific info to propagate.\n   *\n   * The tracestate field value is a `list` as defined below. The `list` is a\n   * series of `list-members` separated by commas `,`, and a list-member is a\n   * key/value pair separated by an equals sign `=`. Spaces and horizontal tabs\n   * surrounding `list-members` are ignored. There can be a maximum of 32\n   * `list-members` in a `list`.\n   * More Info: https://www.w3.org/TR/trace-context/#tracestate-field\n   *\n   * Examples:\n   *     Single tracing system (generic format):\n   *         tracestate: rojo=00f067aa0ba902b7\n   *     Multiple tracing systems (with different formatting):\n   *         tracestate: rojo=00f067aa0ba902b7,congo=t61rcWkgMzE\n   */\n  traceState?: TraceState;\n}\n"]}