{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;;;;;;GAcG;;;AAGH,yCAAiE;AAAxD,uHAAA,8BAA8B,OAAA;AAKvC,eAAe;AACf,6CAAmE;AAA1D,2GAAA,gBAAgB,OAAA;AAAE,uGAAA,YAAY,OAAA;AAIvC,YAAY;AACZ,sDAAyD;AAAhD,kHAAA,iBAAiB,OAAA;AAC1B,sCAMsB;AAHpB,qGAAA,YAAY,OAAA;AAMd,eAAe;AACf,iDAAsD;AAA7C,4GAAA,eAAe,OAAA;AAGxB,2CAgB0B;AAfxB,mGAAA,SAAS,OAAA;AAsBX,mBAAmB;AACnB,qEAMyC;AAFvC,yHAAA,oBAAoB,OAAA;AACpB,yHAAA,oBAAoB,OAAA;AAOtB,mDAAmE;AAA1D,0GAAA,WAAW,OAAA;AACpB,mEAAkE;AAAzD,0HAAA,mBAAmB,OAAA;AAE5B,yDAA0E;AAAjE,kHAAA,gBAAgB,OAAA;AAEzB,+CAA6C;AAApC,qGAAA,QAAQ,OAAA;AAGjB,yCAA4D;AAAvC,wGAAA,cAAc,OAAA;AACnC,mDAAiD;AAAxC,yGAAA,UAAU,OAAA;AAEnB,gDAA0D;AAAjD,yGAAA,gBAAgB,OAAA;AAIzB,+DAImC;AAHjC,uHAAA,kBAAkB,OAAA;AAClB,mHAAA,cAAc,OAAA;AACd,kHAAA,aAAa,OAAA;AAEf,yEAIwC;AAHtC,wHAAA,cAAc,OAAA;AACd,yHAAA,eAAe,OAAA;AACf,8HAAA,oBAAoB,OAAA;AAItB,sEAAsE;AACtE,qCAAqC;AACrC,+CAAwC;AAO/B,wFAPA,qBAAO,OAOA;AANhB,yCAAkC;AAMhB,qFANT,eAAI,OAMS;AALtB,+CAAwC;AAKhB,wFALf,qBAAO,OAKe;AAJ/B,uDAAgD;AAIf,4FAJxB,6BAAW,OAIwB;AAH5C,2CAAoC;AAGU,sFAHrC,iBAAK,OAGqC;AACnD,kBAAkB;AAClB,kBAAe;IACb,OAAO,EAAP,qBAAO;IACP,IAAI,EAAJ,eAAI;IACJ,OAAO,EAAP,qBAAO;IACP,WAAW,EAAX,6BAAW;IACX,KAAK,EAAL,iBAAK;CACN,CAAC", "sourcesContent": ["/*\n * Copyright The OpenTelemetry Authors\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *      https://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\nexport { BaggageEntry, BaggageEntryMetadata, Baggage } from './baggage/types';\nexport { baggageEntryMetadataFromString } from './baggage/utils';\nexport { Exception } from './common/Exception';\nexport { HrTime, TimeInput } from './common/Time';\nexport { Attributes, AttributeValue } from './common/Attributes';\n\n// Context APIs\nexport { createContextKey, ROOT_CONTEXT } from './context/context';\nexport { Context, ContextManager } from './context/types';\nexport type { ContextAPI } from './api/context';\n\n// Diag APIs\nexport { DiagConsoleLogger } from './diag/consoleLogger';\nexport {\n  DiagLogFunction,\n  DiagLogger,\n  DiagLogLevel,\n  ComponentLoggerOptions,\n  DiagLoggerOptions,\n} from './diag/types';\nexport type { DiagAPI } from './api/diag';\n\n// Metrics APIs\nexport { createNoopMeter } from './metrics/NoopMeter';\nexport { MeterOptions, Meter } from './metrics/Meter';\nexport { MeterProvider } from './metrics/MeterProvider';\nexport {\n  ValueType,\n  Counter,\n  Gauge,\n  Histogram,\n  MetricOptions,\n  Observable,\n  ObservableCounter,\n  ObservableGauge,\n  ObservableUpDownCounter,\n  UpDownCounter,\n  BatchObservableCallback,\n  MetricAdvice,\n  MetricAttributes,\n  MetricAttributeValue,\n  ObservableCallback,\n} from './metrics/Metric';\nexport {\n  BatchObservableResult,\n  ObservableResult,\n} from './metrics/ObservableResult';\nexport type { MetricsAPI } from './api/metrics';\n\n// Propagation APIs\nexport {\n  TextMapPropagator,\n  TextMapSetter,\n  TextMapGetter,\n  defaultTextMapGetter,\n  defaultTextMapSetter,\n} from './propagation/TextMapPropagator';\nexport type { PropagationAPI } from './api/propagation';\n\n// Trace APIs\nexport { SpanAttributes, SpanAttributeValue } from './trace/attributes';\nexport { Link } from './trace/link';\nexport { ProxyTracer, TracerDelegator } from './trace/ProxyTracer';\nexport { ProxyTracerProvider } from './trace/ProxyTracerProvider';\nexport { Sampler } from './trace/Sampler';\nexport { SamplingDecision, SamplingResult } from './trace/SamplingResult';\nexport { SpanContext } from './trace/span_context';\nexport { SpanKind } from './trace/span_kind';\nexport { Span } from './trace/span';\nexport { SpanOptions } from './trace/SpanOptions';\nexport { SpanStatus, SpanStatusCode } from './trace/status';\nexport { TraceFlags } from './trace/trace_flags';\nexport { TraceState } from './trace/trace_state';\nexport { createTraceState } from './trace/internal/utils';\nexport { TracerProvider } from './trace/tracer_provider';\nexport { Tracer } from './trace/tracer';\nexport { TracerOptions } from './trace/tracer_options';\nexport {\n  isSpanContextValid,\n  isValidTraceId,\n  isValidSpanId,\n} from './trace/spancontext-utils';\nexport {\n  INVALID_SPANID,\n  INVALID_TRACEID,\n  INVALID_SPAN_CONTEXT,\n} from './trace/invalid-span-constants';\nexport type { TraceAPI } from './api/trace';\n\n// Split module-level variable definition into separate files to allow\n// tree-shaking on each api instance.\nimport { context } from './context-api';\nimport { diag } from './diag-api';\nimport { metrics } from './metrics-api';\nimport { propagation } from './propagation-api';\nimport { trace } from './trace-api';\n\n// Named export.\nexport { context, diag, metrics, propagation, trace };\n// Default export.\nexport default {\n  context,\n  diag,\n  metrics,\n  propagation,\n  trace,\n};\n"]}