# Firebase Test Data Upload Scripts

This directory contains scripts to upload test resume data to your Firebase Firestore database for testing the Resume Builder app.

## Quick Setup (Recommended)

```bash
cd scripts
npm install
npm run setup
```

The setup script will guide you through the configuration process!

## Manual Setup Instructions

### 1. Install Dependencies

```bash
cd scripts
npm install
```

### 2. Firebase Service Account Setup

#### Method 1: Service Account Key File (Recommended)

1. **Open Firebase Console:**
   - Go to [Firebase Console](https://console.firebase.google.com/)
   - Select your project

2. **Navigate to Service Accounts:**
   - Click the gear icon (⚙️) → "Project settings"
   - Click on the "Service accounts" tab

3. **Generate Private Key:**
   - Click "Generate new private key" button
   - A dialog will appear warning about keeping the key secure
   - Click "Generate key"

4. **Download and Setup:**
   - A JSON file will be downloaded (usually named like `your-project-name-firebase-adminsdk-xxxxx.json`)
   - Rename this file to `serviceAccountKey.json`
   - Move it to the `scripts` directory

5. **Verify Setup:**
   ```bash
   # Check that the file exists
   ls scripts/serviceAccountKey.json
   ```

#### Method 2: Environment Variable (Alternative)

If you prefer not to use a file:

```bash
# Set environment variables
export GOOGLE_APPLICATION_CREDENTIALS="/path/to/your/serviceAccountKey.json"
export FIREBASE_PROJECT_ID="your-project-id"
```

**Important:** Never commit the `serviceAccountKey.json` file to version control!

### 3. Configure Project Settings

Edit the `upload_test_data.js` file and update:

```javascript
// Replace with your actual Firebase project ID
projectId: 'your-project-id'

// Replace with actual user ID from Firebase Auth
const TEST_USER_ID = 'test-user-123';
```

To get a real user ID:
1. Create a user account in your app
2. Check Firebase Auth console for the user's UID
3. Use that UID in the script

## Available Scripts

### Check Firebase Setup

```bash
npm run check
```

This verifies your Firebase configuration before uploading data. It checks:
- Firestore API is enabled
- Service account permissions
- Database access
- Security rules

### Upload Single Test Resume

```bash
npm run upload-test-data
```

This uploads a comprehensive resume for "Sarah Johnson" with:
- Complete personal information
- 3 work experiences with achievements
- 2 education entries
- 2 detailed projects
- Multiple skill categories
- 3 languages
- 3 certifications
- Social media links

### Upload Multiple Test Resumes

```bash
npm run upload-multiple
```

This uploads 3 different test resumes with varying levels of completeness to test different scenarios.

### Migrate Existing Data

```bash
npm run migrate
```

Migrates existing resume documents to use the new subcollection structure for better performance and scalability.

```bash
npm run migrate-cleanup
```

Same as migrate, but also removes old array fields from main documents after migration.

## New Collection Structure

The updated scripts now use a more scalable Firestore structure:

### Main Document (`/resumes/{resumeId}`)
```javascript
{
  id: "resume-uuid",
  userId: "user-uid",
  personalInfo: { firstName, lastName, email, ... },
  summary: "Professional summary text",
  createdAt: "2025-01-01T00:00:00.000Z",
  updatedAt: "2025-01-01T00:00:00.000Z"
}
```

### Subcollections
- `/resumes/{resumeId}/workExperience/{expId}`
- `/resumes/{resumeId}/education/{eduId}`
- `/resumes/{resumeId}/projects/{projectId}`
- `/resumes/{resumeId}/skills/{skillCategoryId}`
- `/resumes/{resumeId}/languages/{languageId}`
- `/resumes/{resumeId}/certifications/{certId}`
- `/resumes/{resumeId}/socialMedia/{socialId}`

### Benefits
- ✅ **Better Performance**: Faster queries and smaller document sizes
- ✅ **Scalability**: No 1MB document size limit issues
- ✅ **Flexibility**: Query individual sections independently
- ✅ **Cost Efficiency**: Only load data you need

## Test Data Overview

### Sarah Johnson (Complete Resume)
- **Role:** Senior Mobile Developer
- **Experience:** 5+ years
- **Skills:** Flutter, React, Node.js, Firebase
- **Education:** Stanford BS + UC Berkeley MS
- **Projects:** EcoTracker App, TaskFlow Platform
- **Certifications:** Google Cloud, AWS, Flutter

### Additional Test Profiles (Multiple Upload)
1. **Michael Chen** - Junior Developer with minimal experience
2. **Emily Rodriguez** - Mid-level Full-Stack Developer
3. **David Kim** - Senior Backend Engineer

## Testing the Type Casting Fix

After uploading the test data, you can test the type casting fix by:

1. **Online Test:** Load the app while connected to internet - data should load from Firebase
2. **Offline Test:** 
   - Load the app online first (to cache data locally)
   - Disconnect from internet
   - Force close and reopen the app
   - Navigate to "My Resumes" - should load from local storage without the type casting error

## Troubleshooting

### Common Issues

1. **"Cloud Firestore API has not been used" error:**
   - **Solution:** Enable Firestore in your Firebase project
   - Go to Firebase Console → Your Project → Firestore Database
   - Click "Create database" → Choose "Start in test mode"
   - Select a location and wait for setup to complete

2. **"Permission denied" error:**
   - Check that your service account key is correct
   - Verify Firebase project ID is correct
   - Ensure Firestore security rules allow writes
   - Run `npm run check` to diagnose the issue

3. **"Module not found" error:**
   - Run `npm install` in the scripts directory
   - Check that all dependencies are installed

4. **"User not authenticated" error in app:**
   - Make sure the TEST_USER_ID matches a real user in Firebase Auth
   - The user must be authenticated in the app to see their resumes

5. **Project ID still shows "your-project-id":**
   - Run `npm run setup` to configure your project
   - Or manually update the project ID in the script files

### Firestore Security Rules

Make sure your Firestore rules allow the test user to read/write resumes:

```javascript
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    match /resumes/{resumeId} {
      allow read, write: if request.auth != null && 
        request.auth.uid == resource.data.userId;
    }
  }
}
```

## Cleanup

To remove test data:

```bash
# You can delete test documents from Firebase Console
# Or create a cleanup script if needed
```

## Security Notes

- Never commit `serviceAccountKey.json` to version control
- Add `serviceAccountKey.json` to your `.gitignore`
- Use environment variables for production deployments
- Limit service account permissions to only what's needed
